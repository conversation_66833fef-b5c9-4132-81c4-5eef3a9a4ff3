import { Divider, Text } from '@mantine/core';
import { MODAL_NAME, PROMO_TYPE } from '@/constants';
import { Link } from 'react-router-dom';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { useCartStore } from '@/apps/shop/stores/useCartStore/useCartStore';
import { ProductCartHorizontal } from '@/libs/products/components/ProductCardHorizontal/ProductCartHorizontal';
import styles from './CartVendorPromoItem.module.css';
import type { CartItemType } from '@/libs/cart/types';
import { CartVendorPromotionDisplay } from './CartVendorPromotionDisplay';
import { getPromotionDetails } from './promotionUtils';
import { CartVendorPromoItemActions } from './CartVendorPromoItemActions';

type CartVendorPromoItemProps = {
  items: CartItemType[];
};

export const CartVendorPromoItem = ({ items }: CartVendorPromoItemProps) => {
  const { openModal } = useModalStore();
  const { addToCart } = useCartStore();

  // Only show if there are items
  if (!items || items.length === 0) return null;

  // We'll use the first item for some shared info (like vendor, etc)
  const firstItem = items[0];
  const { product } = firstItem;
  const promotions = product.promotions || [];
  const promotion = promotions.find((promo) => promo.type === 'buy_x_get_y');

  // Helper for remove modal
  const handleOpenRemoveProductModal = (item: CartItemType) => {
    openModal({
      name: MODAL_NAME.REMOVE_PRODUCT_FROM_CART,
      product: item.product,
      productOfferId: item.productOfferId,
    });
  };

  return (
    <ProductCartHorizontal
      productOfferId={firstItem.productOfferId}
      product={product}
      content={
        <div className="grid pl-4">
          <span className="text-sxs font-medium text-green-600/90">
            Promotion • {PROMO_TYPE['buy_x_get_y']}
          </span>
          {items.map((item) => {
            const offer = item.product.offers.find(
              ({ id }) => item.productOfferId === id,
            );
            if (!offer) return null;

            const productUrl = getProductUrl(
              item.product.id,
              item.productOfferId,
            );

            return (
              <div key={item.id} className="mb-4">
                <Link to={productUrl} className={styles.titleWrap}>
                  <h3 className="max-w-9/10 text-sm leading-5 font-medium text-black">
                    {promotion?.name}
                  </h3>
                </Link>
                <div className="m-1 mb-3 flex text-center">
                  <span className="text-xs text-neutral-500/80">
                    SKU:
                    <span className="ml-0.5 text-xs font-medium text-[#333333]">
                      {offer.vendorSku}
                    </span>
                  </span>
                  {item.product.manufacturer ? (
                    <>
                      <Divider orientation="vertical" h="1rem" mx="md" />
                      <Text ml="2px" c="#344054" size="xs">
                        {item.product.manufacturer}
                      </Text>
                    </>
                  ) : null}
                </div>
                <div className="divider-h"></div>
                {/* Display promotion for this item */}
                <CartVendorPromotionDisplay
                  product={item.product}
                  offer={offer}
                  quantity={item.quantity}
                  productOfferId={item.productOfferId}
                />
                <div className="divider-h mb-3"></div>
                <div className="flex w-full items-center justify-between">
                  {(() => {
                    const { totalProducts, total } = getPromotionDetails({
                      quantity: item.quantity,
                      product: item.product,
                      offerId: item.productOfferId,
                    });
                    return (
                      <>
                        <span className="text-xs text-neutral-600">
                          You’re getting total of{' '}
                          <strong>{totalProducts}</strong> products
                        </span>
                        <span className="text-xs text-neutral-500">
                          Promotional Savings:{' '}
                          <span className="text-sm font-medium text-black">
                            {item?.subtotal &&
                            total - Number(item?.subtotal) > 0
                              ? `$${(total - Number(item?.subtotal)).toFixed(2)}`
                              : '$0.00'}
                          </span>
                        </span>
                      </>
                    );
                  })()}
                </div>
              </div>
            );
          })}
        </div>
      }
      actions={null}
    />
  );
};
